import { Keyboard } from '@mrn/react-native';
import { create } from 'zustand';

const defaultUiState = {
    panelOpen: false,
    panelRef: [] as any[],
    poiSelectorOpen: false,
    showHome: true,
};
type UiState = typeof defaultUiState;

const getActions = (set: Setter, get: Getter) => ({
    reset: () => {
        set(defaultUiState);
    },
    setPanelOpen: (v: boolean) => {
        // 打开面板时关闭键盘
        if (v) {
            Keyboard.dismiss();
        }
        set({ panelOpen: v });
    },
    setPanelRef: (v: any) => {
        const { panelRef } = get();
        set({ panelRef: [v, ...panelRef] });
    },
    setPoiSelectorOpen: (v: boolean) => {
        // 打开商家选择器时关闭键盘
        if (v) {
            Keyboard.dismiss();
        }
        set({ poiSelectorOpen: v });
    },
    setShowHome: (v: boolean) => {
        set({ showHome: v });
    },
});
export type UiStateAndActions = UiState & ReturnType<typeof getActions>;
type Setter = (v: Partial<UiState>) => void;
type Getter = () => UiStateAndActions;

export const useUiState = create<UiStateAndActions>((set, get) => ({
    ...defaultUiState,
    ...getActions(set, get),
}));
