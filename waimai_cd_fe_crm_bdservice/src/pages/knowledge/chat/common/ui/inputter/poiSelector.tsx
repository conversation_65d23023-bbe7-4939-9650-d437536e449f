import usePoiList, { PoiItem } from '@src/pages/knowledge/chat/common/service/poiList';
import noContentImg from '@src/assets/images/no-content.png';
import { Avatar, Empty, Input, List, Radio, Space, Spin } from 'antd';
import VirtualList from 'rc-virtual-list';
import { useEffect, useState } from 'react';
import { usePrevious } from 'ahooks';

const PoiSelector = ({ onChange }) => {
    const { onScroll, data, setSearchValue, loading } = usePoiList();
    const [activePoi, setActivePoi] = useState<PoiItem>();
    const previousData = usePrevious(data);
    useEffect(() => {
        if (previousData?.list?.length !== data?.list?.length) {
            setActivePoi(data?.list?.[0]);
        }
    }, [data?.list]);
    useEffect(() => {
        // 搜索中重置选中项，阻止提交
        if (loading) {
            onChange?.(undefined);
            return;
        }
        onChange?.(activePoi);
    }, [activePoi, loading]);
    return (
        <Space direction={'vertical'} style={{ width: '100%' }}>
            <Input.Search
                placeholder={'请输入商家名称'}
                onChange={e => setSearchValue(e.target.value)}
                style={{ width: '100%' }}
            />
            <Spin spinning={loading}>
                <List style={{ width: '100%' }} locale={{ emptyText: '暂无数据' }}>
                    {!data?.list?.length ? (
                        <Empty
                            description={'仅支持查询您名下的商家，请确保输入信息无误'}
                            image={noContentImg}
                            style={{ margin: '30px auto' }}
                        />
                    ) : (
                        <VirtualList
                            data={data?.list || []}
                            height={400}
                            itemHeight={47}
                            style={{ scrollbarWidth: 'none' }}
                            itemKey={item => item?.id}
                            onScroll={onScroll}
                        >
                            {item => {
                                return (
                                    <Space onClick={() => setActivePoi(item)} className={'pointer'}>
                                        <Radio value={item.id} checked={activePoi?.id === item.id} />
                                        <Avatar src={item.url} />
                                        <div>
                                            <div className={'f_14 c_222'}>{item.name}</div>
                                            <div className={'f_12 c_666'}>ID: {item.id}</div>
                                        </div>
                                    </Space>
                                );
                            }}
                        </VirtualList>
                    )}
                </List>
            </Spin>
        </Space>
    );
};
export default PoiSelector;
