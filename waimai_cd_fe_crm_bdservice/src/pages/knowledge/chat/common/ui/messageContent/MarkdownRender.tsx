import MediaRender from '@src/pages/knowledge/chat/common/ui/messageContent/MediaRender';
import './MarkdownRender.scss';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import BaseText from '@src/pages/knowledge/chat/common/ui/messageContent/BaseTextCard';
import { getStyle } from '@src/pages/knowledge/chat/common/ui/message/indexNew';

const castMediaItem = (deltaWithType: any[]) => {
    const res: any[] = [];
    // 图片和视频同行展示
    for (let i = 0; i < deltaWithType.length; i++) {
        const cur = deltaWithType[i];
        if (!['image', 'video'].includes(cur.type)) {
            res.push(cur);
            continue;
        }

        const lastEle = res[res.length - 1] || { type: '' };
        if (lastEle.type === 'media') {
            lastEle.insert.media.push(cur.insert);
            continue;
        }
        res.push({
            type: 'media',
            insert: { media: [cur.insert] },
        });
    }
    return res;
};

const pic = {
    test: (str: string) => /!\[.*?]\((.*?)\)/.test(str),
    text2Obj: (str: string) => {
        const value = str.split(/!\[.*?]\((.*?)\)/).filter(v => v);
        return { url: value[0] };
    },
};

// 从md文本解析图片数据
export const splitMediaFromMarkdown = (md: string) => {
    const splitPattern = /(!\[.*?]\(.*?\))/; // md图片

    const castMarkdown = (children: string) => {
        let res: any[] = [];
        if (pic.test(children)) {
            const parts = children.split(splitPattern);
            res = parts
                .filter(v => v && !/^\s*$/.test(v))
                .filter(Boolean)
                .map(p => {
                    if (pic.test(p)) {
                        const { url } = pic.text2Obj(p);
                        return {
                            type: 'image',
                            insert: { image: url },
                        };
                    }
                    return {
                        type: 'text',
                        insert: p,
                    };
                });
        } else {
            res = [{ type: 'text', insert: children }];
        }
        return res;
    };
    return castMediaItem(castMarkdown(md));
};

const MarkdownRender = ({ data }: { data: string }) => {
    const renderData = splitMediaFromMarkdown(data);

    if (!data) {
        return null;
    }
    return (
        <>
            {renderData.map((item, index) => {
                if (item.type === 'media') {
                    return <MediaRender item={item} key={index} />;
                }
                return (
                    <ReactMarkdown
                        key={item.insert}
                        children={item.insert}
                        remarkPlugins={[remarkGfm]}
                        components={{
                            a: props => {
                                const { href, children } = props;
                                const Comp: any = BaseText;
                                return (
                                    <Comp
                                        key={'react-md'}
                                        text={children as string}
                                        link={href}
                                        style={getStyle([
                                            { wordBreak: 'break-all' },
                                            item.attributes?.bold ? { fontWeight: 'bold' } : undefined,
                                            item.attributes?.color ? { color: item.attributes.color } : undefined,
                                            { whiteSpace: 'pre-wrap' },
                                        ])}
                                    />
                                );
                            },
                            table: props => (
                                <div className="markdown-table-wrapper">
                                    <table {...props} className="markdown-table" />
                                </div>
                            ),
                            th: props => <th {...props} className="markdown-table-header" />,
                            td: props => <td {...props} className="markdown-table-cell" />,
                        }}
                    />
                );
            })}
        </>
    );
};
export default MarkdownRender;
