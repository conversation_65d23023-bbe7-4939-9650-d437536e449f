import { CSSProperties } from 'react';
import BaseText from '../messageContent/BaseTextCard';
import MediaRender from '../messageContent/MediaRender';
import OptionMessage from '../messageContent/OptionMessageCard';
import { CardWithAvatarComp } from '@src/pages/knowledge/components/editor/customComps/cardWithAvatar';
import { Button, Descriptions, Row } from 'antd';
import openLink from '../../utils/openLink';
import { SelectorComp, SelectorItemComp } from '@src/pages/knowledge/components/editor/customComps/selector';
import { Config, SelectorItemMessage } from '@src/pages/knowledge/message';
import { EntryPoint, EntryPointType } from '../../service/sendMessage/sendMessage';
import TableMessage from '../messageContent/TableMessageCard';
import NewPoiWeightingCard from '../messageContent/NewPoiWeightingCard';
import FormCard from '../messageContent/FormCard';
import CollapsibleText from '../messageContent/CollapsibleText';
import ReferenceDoc from '../messageContent/ReferenceDoc';
import Title from '../messageContent/Title';

export const getStyle = (style: (CSSProperties | undefined)[]) => {
    return style.filter(Boolean).reduce((acc, cur) => {
        return { ...acc, ...cur };
    }, {});
};

export const renderStandardQuestionItem = (sendMessage, serverId, history) => (item, index) => {
    switch (item.type) {
        case 'styledText':
        case 'link':
        case 'text':
            return (
                <BaseText
                    key={item.localId}
                    text={item.insert}
                    link={item.attributes?.link} // 链接，如果该项有内容则按链接展示
                    style={getStyle([
                        { wordBreak: 'break-all' }, // 强制换行，防止英文过长超出容器的情况
                        item.attributes?.bold ? { fontWeight: 'bold' } : undefined, // 支持加粗
                        item.attributes?.color ? { color: item.attributes.color } : undefined, // 支持颜色
                        { whiteSpace: 'pre-wrap' }, // 保留换行符
                    ])}
                />
            );
        case 'media':
            return <MediaRender item={item} />;
        case 'options':
            return <OptionMessage key={item.localId} item={item as any} style={index !== 0 ? { marginTop: 16 } : {}} />;
        case 'cardWithAvatar':
            return <CardWithAvatarComp value={item.insert.cardWithAvatar as any} key={item.localId} />;
        case 'buttons':
            return (
                <Row justify={'end'} key={item.localId}>
                    {item.insert.buttons.map(v => {
                        return (
                            <Button
                                key={v.text}
                                type={v.type === 'primary' ? 'primary' : 'default'}
                                style={{ background: v.color }}
                                onClick={() => {
                                    if (v.url) {
                                        openLink(v.url);
                                    }
                                }}
                            >
                                {v.text}
                            </Button>
                        );
                    })}
                </Row>
            );
        case 'selector':
            return (
                <SelectorComp
                    key={item.localId}
                    value={item.insert.selector}
                    onItemClick={item => {
                        sendMessage(
                            {
                                config: {
                                    style: {
                                        backgroundColor: '#fff',
                                        width: '100%',
                                    },
                                },
                                data: [
                                    {
                                        type: 'config',
                                        insert: {
                                            config: {
                                                style: {
                                                    backgroundColor: '#fff',
                                                    width: '100%',
                                                },
                                            },
                                        },
                                    } as Config,
                                    {
                                        type: 'selectorItem',
                                        insert: {
                                            selectorItem: item,
                                        },
                                    } as SelectorItemMessage,
                                ],
                            },
                            {
                                entryPointType: EntryPointType.REJECT_SELECTOR,
                                entryPoint: EntryPoint.poi_reject_selector,
                            },
                        );
                    }}
                />
            );
        case 'selectorItem':
            return <SelectorItemComp value={item.insert.selectorItem} needCard={false} key={item.localId} />;
        case 'table':
            return <TableMessage {...item.insert.table} style={index !== 0 ? { marginTop: 10 } : undefined} />;
        case 'newPoiWeightingCard':
            return <NewPoiWeightingCard {...item.insert.newPoiWeightingCard} />;
        case 'form':
            return <FormCard {...item.insert.form} history={history} />;
        case 'collapsibleText':
            return <CollapsibleText {...item.insert.collapsibleText} msgId={serverId} history={history} />;
        case 'referenceDoc':
            return <ReferenceDoc {...item.insert.referenceDoc} />;
        case 'title':
            return <Title {...item.insert.title} />;
        case 'descriptions':
            return <Descriptions {...item.insert.descriptions} />;
        default:
            return null;
    }
};
