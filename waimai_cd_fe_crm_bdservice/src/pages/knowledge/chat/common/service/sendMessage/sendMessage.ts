import { APISpec } from '@mfe/cc-api-caller-pc';
import { message, message as antdMessage } from 'antd';
import useAiStore from '../../data/core';
import { AdditionMessage, Message, MessageData, MessageFrom, MessageStatus } from '../../type/message';
import _ from 'lodash';
import { useLatest } from 'ahooks';
import { parseDataFromMsgComponents } from '@src/pages/knowledge/chat/common/service/sendMessage/adaptServerMessage';

export const enum AbilityType {
    GENERAL = 1,
    JUMP,
    SERVICE_SCORE,
    WELCOME,
}
export enum EntryPointType {
    USER = 1,
    VOICE,
    ASSOCIATION,
    TOOL,
    WELCOME,
    SECTION,
    POI_SELECTOR, // 商家选择器
    REJECT_SELECTOR, // 驳回原因选择器
}
export enum EntryPoint {
    tab = 'tab',
    option_list = 'option_list',
    input = 'input',
    toolbar = 'toolbar',
    poi_select = 'poi_select',
    input_prediction = 'input_prediction',
    voice_input = 'voice_input',
    poi_reject_selector = 'poi_reject_selector',
    refresh = 'refresh',
    floating_option_list = 'floating_option_list',
    form_input = 'form_input',
    picture_tip = 'picture_tip',
}

// 生成全局唯一id作为渲染的key
const generateLocalId = (prefix = 'question_') => {
    return _.uniqueId(prefix);
};

// TODO 支持配置
const generateFetchAnswerParams = (questionRes: any, extraParams = {}) => {
    return { ...questionRes, ...extraParams };
};

export type QuestionParams = Partial<APISpec['/bee/v1/bdaiassistant/submitQuery']['request']> & {
    entryPointType?: EntryPointType;
    entryPoint?: EntryPoint | string;
};

export const useStartPolling = () => {
    const setPollingTimer = useAiStore(v => v.setPollingTimer);
    const startTypingMessage = useAiStore(v => v.startTypingMessage);
    const needPlay = useAiStore(v => v.typing.needPlay);
    const needPlayLatest = useLatest(needPlay);
    const getMessageArray = useAiStore(v => v.getMessageArray);
    const onOpenPoiSelector = useAiStore(v => v.onOpenPoiSelector);
    const appendMessage = useAiStore(v => v.appendMessage);
    const modifyMessage = useAiStore(v => v.modifyMessage);

    const getStoreEle = useAiStore(v => v.getStoreEle);

    return questionResData => {
        // 添加空回答消息
        const localAnswerId = generateLocalId('answer_');
        appendMessage({
            data: [],
            id: localAnswerId,
            localStatus: MessageStatus.generating,
            from: MessageFrom.left,
            status: MessageStatus.generating,
        });
        needPlayLatest?.current && startTypingMessage(localAnswerId);

        // 轮询拉取回答
        const PollingTimeout = 500;
        const MaxRetryCount = 5;
        let errorCount = 0;
        const pollingMessage = () => {
            const pollingTimer = setTimeout(async () => {
                // 发现clearTimeout无法cover住停止回答的能力，在此处增加一次保险
                if (localAnswerId !== getStoreEle('pollingMessageLocalId')) {
                    return;
                }
                // 连续多次重试失败则停止拉取回答
                if (errorCount > MaxRetryCount) {
                    antdMessage.error('网络异常，请稍后重试');
                    modifyMessage(localAnswerId, currentMessage => ({
                        ...currentMessage,
                        localStatus: MessageStatus.error,
                        status: MessageStatus.done,
                        data:
                            currentMessage.data?.length === 0
                                ? [{ insert: '非常抱歉服务器去送外卖了，再问一句试试呀', type: 'text' }]
                                : currentMessage.data,
                        typingData:
                            currentMessage.data?.length === 0
                                ? [{ insert: '非常抱歉服务器去送外卖了，再问一句试试呀', type: 'text' }]
                                : currentMessage.data,
                    }));
                    return;
                }

                const theMessage = getMessageArray().find(v => v.id === localAnswerId);
                const api = getStoreEle('api');
                const answerRes = await api.fetchAnswer(
                    generateFetchAnswerParams(questionResData, { msgId: theMessage?.serverId }),
                );

                // 如果获取到回答时用户已经主动终止，则丢弃本次回答内容
                if (localAnswerId !== getStoreEle('pollingMessageLocalId')) {
                    return;
                }

                // 失败则放弃本轮数据&继续拉取数据
                if (answerRes.code !== 0) {
                    errorCount++;
                    pollingMessage();
                    return;
                }

                // 成功则清零连续失败次数
                errorCount = 0;

                // 状态不是done则继续拉取
                if (answerRes.data.status !== MessageStatus.done) {
                    pollingMessage();
                }

                // tags包含poiId则唤起商家选择器
                if (answerRes.data.tags?.includes('poiId')) {
                    onOpenPoiSelector();
                }

                // 主要处理逻辑
                // JSON.parse失败则认为服务器数据返回的纯文本
                let messageDataParsed: Message[] = [
                    {
                        insert: answerRes.data.currentContent,
                        type: 'text',
                        localId: generateLocalId('messageContent_'),
                    },
                ];
                try {
                    messageDataParsed = Array.isArray(answerRes.data.currentContent)
                        ? answerRes.data.currentContent
                        : (JSON.parse(answerRes.data.currentContent) as Message[]);
                    // 数字字符串不会产生错误，但不符合预期
                    if (typeof messageDataParsed === 'number') {
                        throw new Error('currentContent是字符串型数字');
                    }
                } catch (e) {
                    // 解析失败则直接展示
                    console.log(e);
                }

                // 如果没有有效数据仍需更新状态，防止此时回答结束
                if (messageDataParsed.length === 0) {
                    modifyMessage(localAnswerId, { status: answerRes.data.status });
                    return;
                }

                const newMessageData = parseDataFromMsgComponents(messageDataParsed);

                modifyMessage(localAnswerId, currentMessage => {
                    return {
                        ...currentMessage,
                        serverId: answerRes.data.msgId,
                        status: answerRes.data.status,
                        ...newMessageData,
                        data: [...(currentMessage?.data || []), ...newMessageData.data],
                    };
                });

                // 主要处理逻辑
            }, PollingTimeout);

            // 保存timer供停止回答的操作
            setPollingTimer(pollingTimer);
        };
        pollingMessage();
    };
};

const useSendMessage = () => {
    const appendMessage = useAiStore(v => v.appendMessage);
    const modifyMessage = useAiStore(v => v.modifyMessage);
    const setDragged = useAiStore(v => v.setDragged);
    const existPollingMessage = useAiStore(v => v.existPollingMessage);
    const getStoreEle = useAiStore(v => v.getStoreEle);
    const clearFile = useAiStore(v => v.clearFile);
    const setShowHome = useAiStore(v => v.setShowHome);

    const startPolling = useStartPolling();

    return async (
        question: string | Partial<MessageData>,
        extraParams: QuestionParams,
        withFile = true,
        callback = () => {},
    ) => {
        if (existPollingMessage()) {
            message.error('请等待当前回答完成');
            return;
        }
        setShowHome(false);
        callback?.();
        setDragged(false); // 自动打开滚动开关
        // 添加问题消息
        const localQuestionId = generateLocalId();

        const isString = typeof question === 'string';
        let data = isString ? (question ? [{ insert: question, type: 'text' }] : []) : question.data || [];
        if (isString) {
            // question允许为空，场景：只有图片
            data = question ? [{ insert: question, type: 'text' }] : [];
        } else {
            data = question.data || [];
        }
        const draftQuestion: any = {
            ...(isString ? {} : question),
            data,
        };
        const file = getStoreEle('file').filter(v => v.status === 'success');
        if (withFile && file.length > 0) {
            draftQuestion.data = [
                {
                    type: 'addition',
                    insert: {
                        addition: {
                            additionList: file,
                        },
                    },
                } as AdditionMessage,
                ...draftQuestion.data,
            ];
            clearFile();
        }
        appendMessage({
            ...(typeof draftQuestion === 'string' ? {} : draftQuestion),
            data:
                typeof draftQuestion === 'string'
                    ? [{ insert: draftQuestion, type: 'text' }]
                    : draftQuestion.data || [],
            id: localQuestionId,
            localStatus: MessageStatus.done,
            from: MessageFrom.right,
            status: MessageStatus.generating,
        });

        // 超时再显示loading状态
        const OVERTIME_TIME = 2000;
        const overtimeTimer = setTimeout(() => {
            modifyMessage(localQuestionId, {
                localStatus: MessageStatus.sending,
            });
        }, OVERTIME_TIME);

        const api = getStoreEle('api');
        // 后端处理逻辑不统一，需要特殊处理
        let questionContent = JSON.stringify(draftQuestion.data);
        if (draftQuestion.data.length === 1 && draftQuestion.data[0].type === 'text') {
            questionContent = draftQuestion.data[0].insert;
        }
        const questionRes = await api.sendMessage({
            content: questionContent,
            ...extraParams,
        });

        // 发生错误
        if (questionRes.code !== 0) {
            clearTimeout(overtimeTimer);
            modifyMessage(localQuestionId, {
                status: MessageStatus.done,
                localStatus: MessageStatus.error,
                retryParams: [draftQuestion, extraParams, withFile, callback],
            });
            return;
        }

        const questionIdFromServer = questionRes.data?.questionMsgId;

        // 更新问题消息
        clearTimeout(overtimeTimer);
        modifyMessage(localQuestionId, {
            localStatus: MessageStatus.done,
            status: MessageStatus.done,
            serverId: questionIdFromServer,
        });

        startPolling(questionRes.data);
    };
};
export default useSendMessage;
