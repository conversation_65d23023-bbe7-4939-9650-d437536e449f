import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react-swc';
import tsconfigPaths from 'vite-tsconfig-paths';
import htmlTemplate from 'vite-plugin-html-template';
import mpa from 'vite-plugin-mpa';
import qiankun from 'vite-plugin-qiankun';
import vitePluginSvgr from 'vite-plugin-svgr';
import path from 'path';

const SSO_COOKIE_SCRIPTS = {
    // eslint-disable-next-line
    test: `<script type="text/javascript" src="https://ssosv.it.test.sankuai.com/sson/web/device/info/script/fast"></script>
      <script type="text/javascript" src="http://ssosv.it.test.sankuai.com/sson/web/device/info/script/fast"></script>`,
    prod: '<script type="text/javascript" src="https://ssosv.sankuai.com/sson/web/device/info/script/fast"></script>',
};

export default defineConfig(({ mode }) => {
    // vite如果需要在build time 感知.env的环境变量，需要使用loadEnv手动加载
    // 如果是runtime， 那么直接通过import.meta.env.VITE_XX拿就行了
    // 注意这个函数运行的时机是build time， node执行时，因此可以使用...解构process.env
    const env = { ...process.env, ...loadEnv(mode, process.cwd()) };
    const {
        PUBLIC_PATH = '',
        VITE_ROUTER_BASE = '',
        DEPLOY_ENV = '', // pipeline
        AWP_DEPLOY_ENV = '', // talos, test可能是test01
        WEBSTATIC_APPKEY = 'com.sankuai.wmmerchantfront.bdservice.static',
    } = env;
    const isProd = ['staging', 'production'].includes(DEPLOY_ENV) || ['staging', 'production'].includes(AWP_DEPLOY_ENV);
    const deployEnv = JSON.stringify(DEPLOY_ENV || AWP_DEPLOY_ENV || '').replace(/(\d+)/g, ''); // 把test01这种转成test

    return {
        appType: 'custom',
        plugins: [
            react(),
            tsconfigPaths(),
            mpa(),
            htmlTemplate({
                pages: {
                    questions: {
                        template: './public/noMenu.html',
                    },
                    'knowledge/chat/common': {
                        template: './public/noMenu.html',
                    },
                    'knowledge/chat': {
                        template: './public/noMenu.html',
                    },
                    'knowledge/chat/redirect': {
                        template: './public/noMenu.html',
                    },
                },
            }),
            qiankun('bdservice'),
            vitePluginSvgr({}),
        ],
        base: PUBLIC_PATH + VITE_ROUTER_BASE,
        build: {
            outDir: 'build' + VITE_ROUTER_BASE, // 指定生成构建代码的目录
            // 适配roo-plus，如果有roo-plus依赖，请自行打开注释
            rollupOptions: {
                plugins: [
                    {
                        name: 'disable-treeshake',
                        transform(_, id) {
                            if (/@roo\/roo\/theme\/default\/\w+\.js$/.test(id)) {
                                return { moduleSideEffects: 'no-treeshake' };
                            }
                        },
                    },
                ],
            },
            commonjsOptions: {
                ignoreTryCatch: false,
            },
        },
        define: {
            'import.meta.env.VITE_APPKEY': JSON.stringify(WEBSTATIC_APPKEY),
            'import.meta.env.VITE_DEPLOY_ENV': JSON.stringify(deployEnv),
            'import.meta.env.VITE_SSO_SCRIPTS': JSON.stringify(
                isProd ? SSO_COOKIE_SCRIPTS.prod : SSO_COOKIE_SCRIPTS.test,
            ),
        },
        server: {
            proxy: {
                '/xianfu/api/bdservice/assistant/common/s3': {
                    // eslint-disable-next-line
                    target: 'http://selftest-240522-201253-419-sl-bdaiassistant-backend.adp.test.sankuai.com',
                    changeOrigin: true,
                    secure: false,
                    rewrite: path => path.replace('/xianfu/api/bdservice/assistant', ''),
                },
                // 本地mock使用
                // '/xianfu/api/bdservice/assistant': {
                //     rewrite: path => path.replace(/^\/xianfu\/api\/bdservice\/assistant/, ''),
                //     // eslint-disable-next-line
                //     target: 'http://127.0.0.1:4523/m1/4077390-0-default',
                //     secure: false,
                //     changeOrigin: true,
                // },
                // '/xianfu/api/bdservice/bee/v1/bdaiassistant/fetchAnswer': {
                //     rewrite: path => path.replace(/^\/xianfu\/api\/bdservice/, ''),
                //     // eslint-disable-next-line
                //     target: 'http://127.0.0.1:4523/m1/4077390-0-default',
                //     secure: false,
                //     changeOrigin: true,
                // },
                '/xianfu/api': {
                    // eslint-disable-next-line
                    // target: 'http://selftest-250610-150033-436-sl-wm-ocrm.waimai.test.sankuai.com',
                    target: 'https://wm-ocrm.waimai.st.sankuai.com',
                    secure: false,
                    changeOrigin: true,
                },
            },
        },
        test: {
            globals: true,
            environment: 'jsdom',
            setupFiles: ['./src/__test__/setup.ts'],

            alias: [
                {
                    // 把这些会报错的第三方库转换成mock组件
                    find: /(^@roo.*)|(@mfe\/cc-api-caller.*)|(^@sailor\/i18n.*)|(^@wmfe\/i18n.*)|(^@mt-material.*)/,
                    replacement: path.resolve(__dirname, './src/fileMock/fileMock.tsx'),
                },
            ],
        },
    };
});
